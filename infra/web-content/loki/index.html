<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loki - Log Aggregation System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            width: 90%;
            text-align: center;
        }
        
        .logo {
            font-size: 3rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 2rem;
        }
        
        .info-box {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 1.5rem;
            margin: 2rem 0;
            text-align: left;
            border-radius: 8px;
        }
        
        .info-box h3 {
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .api-endpoints {
            background: #f1f3f4;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            text-align: left;
        }
        
        .buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            display: inline-block;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            background: #28a745;
            color: white;
            margin-left: 1rem;
        }
        
        .code {
            background: #f8f9fa;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🔍 Loki</div>
        <div class="subtitle">Sistema de Agregación de Logs</div>
        <span class="status">🟢 ACTIVO</span>
        
        <div class="info-box">
            <h3>¿Qué es Loki?</h3>
            <p>Loki es un sistema de agregación de logs horizontalmente escalable, altamente disponible y multi-tenant inspirado en Prometheus. A diferencia de otros sistemas de logging, Loki está construido alrededor de la idea de indexar solo metadatos sobre tus logs: etiquetas (labels).</p>
        </div>
        
        <div class="info-box">
            <h3>Endpoints de API Disponibles</h3>
            <div class="api-endpoints">
GET /loki/api/v1/labels<br>
GET /loki/api/v1/label/{name}/values<br>
GET /loki/api/v1/query<br>
GET /loki/api/v1/query_range<br>
POST /loki/api/v1/push<br>
GET /ready<br>
GET /metrics
            </div>
        </div>
        
        <div class="info-box">
            <h3>Cómo usar Loki</h3>
            <p><strong>1. Visualización:</strong> Usa Grafana para crear dashboards y explorar logs</p>
            <p><strong>2. Consultas:</strong> Utiliza LogQL para consultar logs directamente</p>
            <p><strong>3. Ingesta:</strong> Envía logs usando Promtail, Fluentd, o la API directamente</p>
        </div>
        
        <div class="info-box">
            <h3>Ejemplo de consulta LogQL</h3>
            <div class="api-endpoints">
{job="nginx"} |= "error"<br>
rate({container="app"}[5m])<br>
sum by (level) (rate({service="api"}[1m]))
            </div>
        </div>
        
        <div class="buttons">
            <a href="https://grafana.msarknet.me" class="btn btn-primary">
                📊 Abrir Grafana
            </a>
            <a href="/loki/api/v1/labels" class="btn btn-secondary">
                🔗 API Labels
            </a>
            <a href="https://grafana.com/docs/loki/latest/" class="btn btn-success" target="_blank">
                📚 Documentación
            </a>
        </div>
        
        <div style="margin-top: 2rem; font-size: 0.9rem; color: #666;">
            <p>Para configurar Loki como datasource en Grafana, usa la URL: <span class="code">https://loki.msarknet.me</span></p>
        </div>
    </div>
</body>
</html>
